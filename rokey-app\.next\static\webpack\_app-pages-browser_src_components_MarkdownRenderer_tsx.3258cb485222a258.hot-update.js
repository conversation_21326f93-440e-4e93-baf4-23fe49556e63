"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_MarkdownRenderer_tsx",{

/***/ "(app-pages-browser)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Prism!=!react-syntax-highlighter */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(app-pages-browser)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n}\n// Custom remark plugin to prevent code blocks from being wrapped in paragraphs\nfunction remarkUnwrapCodeBlocks() {\n    return (tree)=>{\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.visit)(tree, 'paragraph', (node, index, parent)=>{\n            if (node.children.length === 1 && node.children[0].type === 'code' && typeof index === 'number' && parent && parent.children) {\n                // Replace paragraph containing only a code block with the code block itself\n                parent.children[index] = node.children[0];\n            }\n        });\n    };\n}\nfunction MarkdownRenderer(param) {\n    let { content, className = '' } = param;\n    _s();\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"markdown-content \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-gray-900 leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 76,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                    remarkPlugins: [\n                        remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        remarkUnwrapCodeBlocks\n                    ],\n                    components: {\n                        // Headers\n                        h1: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h2: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h3: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h4: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Paragraphs\n                        p: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-white break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Bold and italic\n                        strong: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        em: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Lists\n                        ul: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        ol: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        li: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Code blocks and inline code\n                        code: (param)=>{\n                            let { node, inline, className, children, ...props } = param;\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Check if this is a short single-line code snippet that should be treated as enhanced inline\n                                const isShortSnippet = codeContent.length <= 60 && !codeContent.includes('\\n') && !language;\n                                if (isShortSnippet) {\n                                    // Treat short snippets as enhanced inline code with subtle highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-orange-50 text-orange-700 px-1.5 py-0.5 rounded text-sm font-mono border border-orange-200\",\n                                        children: codeContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                                // Handle actual code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                language: language,\n                                                PreTag: \"div\",\n                                                className: \"text-sm\",\n                                                ...props,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Multi-line code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-800 text-gray-100 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-300\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Links\n                        a: (param)=>{\n                            let { children, href } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Tables\n                        table: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-200 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        thead: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tbody: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200 bg-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tr: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        th: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        td: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(MarkdownRenderer, \"Y10BwSn8jQ4wQtRAtRxoscEk2KA=\");\n_c = MarkdownRenderer;\nvar _c;\n$RefreshReg$(_c, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MarkdownRenderer.tsx\n"));

/***/ })

});
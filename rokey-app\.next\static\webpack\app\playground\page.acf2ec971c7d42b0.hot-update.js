"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/CopyButton.tsx":
/*!***************************************!*\
  !*** ./src/components/CopyButton.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CopyButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction CopyButton(param) {\n    let { text, className = '', size = 'sm', variant = 'default', title = 'Copy to clipboard' } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopy = async ()=>{\n        try {\n            await navigator.clipboard.writeText(text);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (err) {\n            console.error('Failed to copy text: ', err);\n            // Fallback for older browsers\n            const textArea = document.createElement('textarea');\n            textArea.value = text;\n            document.body.appendChild(textArea);\n            textArea.focus();\n            textArea.select();\n            try {\n                document.execCommand('copy');\n                setCopied(true);\n                setTimeout(()=>setCopied(false), 2000);\n            } catch (fallbackErr) {\n                console.error('Fallback copy failed: ', fallbackErr);\n            }\n            document.body.removeChild(textArea);\n        }\n    };\n    // Size classes\n    const sizeClasses = {\n        sm: 'w-4 h-4',\n        md: 'w-5 h-5',\n        lg: 'w-6 h-6'\n    };\n    // Button size classes\n    const buttonSizeClasses = {\n        sm: 'p-1.5',\n        md: 'p-2',\n        lg: 'p-2.5'\n    };\n    // Variant-specific styles\n    const variantClasses = {\n        default: 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/80',\n        code: 'text-gray-300 hover:text-white hover:bg-gray-600/80',\n        message: 'text-gray-500 hover:text-gray-700 hover:bg-white/20'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleCopy,\n        className: \"\\n        \".concat(buttonSizeClasses[size], \"\\n        \").concat(variantClasses[variant], \"\\n        rounded transition-all duration-200 cursor-pointer\\n        \").concat(copied ? 'text-green-600' : '', \"\\n        \").concat(className, \"\\n      \"),\n        title: copied ? 'Copied!' : title,\n        children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"\".concat(sizeClasses[size], \" stroke-2\")\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\CopyButton.tsx\",\n            lineNumber: 81,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"\".concat(sizeClasses[size], \" stroke-2\"),\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\CopyButton.tsx\",\n                lineNumber: 84,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\CopyButton.tsx\",\n            lineNumber: 83,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\CopyButton.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(CopyButton, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = CopyButton;\nvar _c;\n$RefreshReg$(_c, \"CopyButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CopyButton.tsx\n"));

/***/ })

});